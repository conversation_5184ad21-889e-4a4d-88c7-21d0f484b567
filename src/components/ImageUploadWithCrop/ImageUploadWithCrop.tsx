import React, { useState, useRef, useCallback } from 'react';
import { Upload, Modal, Avatar, message } from 'antd';
import { LoadingOutlined, CameraOutlined } from '@ant-design/icons';
import ReactCrop, { type Crop, type PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { useLanguage } from '@/hooks/useLanguage';

interface ImageUploadWithCropProps {
  /** 头像地址（用于回显） */
  avatarUrl?: string;
  /** 头像大小 */
  avatarSize?: number;
  /** 用户名（用于默认头像显示） */
  userName?: string;
  /** 上传成功回调 */
  onUploadSuccess?: (url: string) => void;
  /** 自定义上传请求 */
  customRequest?: (options: any) => void;
}

const ImageUploadWithCrop: React.FC<ImageUploadWithCropProps> = ({
  avatarUrl,
  avatarSize = 100,
  userName,
  onUploadSuccess,
  customRequest,
}) => {
  const { t } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(avatarUrl || '');
  const [cropModalVisible, setCropModalVisible] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 80,
    height: 80,
    x: 10,
    y: 10,
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const fileRef = useRef<File>(null);

  // 文件上传前验证
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error(t('messages.uploadImageFailed'));
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error(t('messages.uploadImageSizeFailed'));
      return false;
    }

    // 保存文件引用并显示裁切模态框
    fileRef.current = file;
    const reader = new FileReader();
    reader.onload = () => {
      setImageSrc(reader.result as string);
      setCropModalVisible(true);
    };
    reader.readAsDataURL(file);

    return false; // 阻止自动上传
  };

  // 创建裁切后的canvas
  const getCroppedImg = useCallback(
    (image: HTMLImageElement, crop: PixelCrop): Promise<Blob> => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      canvas.width = crop.width;
      canvas.height = crop.height;

      ctx.drawImage(
        image,
        crop.x * scaleX,
        crop.y * scaleY,
        crop.width * scaleX,
        crop.height * scaleY,
        0,
        0,
        crop.width,
        crop.height
      );

      return new Promise(resolve => {
        canvas.toBlob(blob => resolve(blob!), 'image/jpeg', 0.9);
      });
    },
    []
  );

  // 确认裁切
  const handleCropConfirm = async () => {
    if (!completedCrop || !imgRef.current) return;

    try {
      setLoading(true);
      const croppedImageBlob = await getCroppedImg(
        imgRef.current,
        completedCrop
      );
      const croppedFile = new File(
        [croppedImageBlob],
        fileRef.current?.name || 'avatar.jpg',
        { type: 'image/jpeg' }
      );

      if (customRequest) {
        customRequest({
          file: croppedFile,
          onSuccess: (response: any) => {
            const url = response?.url || URL.createObjectURL(croppedImageBlob);
            setImageUrl(url);
            onUploadSuccess?.(url);
            setLoading(false);
            setCropModalVisible(false);
            message.success(t('messages.uploadSuccess'));
          },
          onError: () => {
            setLoading(false);
            message.error(t('messages.uploadFailed'));
          },
        });
      } else {
        const url = URL.createObjectURL(croppedImageBlob);
        setImageUrl(url);
        onUploadSuccess?.(url);
        setLoading(false);
        setCropModalVisible(false);
        message.success(t('messages.uploadSuccess'));
      }
    } catch {
      message.error(t('messages.uploadFailed'));
      setLoading(false);
    }
  };

  return (
    <>
      <Upload
        name="avatar"
        listType="picture-card"
        showUploadList={false}
        beforeUpload={beforeUpload}
        accept="image/*"
        disabled={loading}
        style={{
          width: avatarSize,
          height: avatarSize,
          border: 'none',
          background: 'transparent',
        }}
      >
        <div className="relative inline-block">
          {imageUrl ? (
            <Avatar
              size={avatarSize}
              src={imageUrl}
              className="cursor-pointer hover:opacity-80"
            />
          ) : (
            <Avatar
              size={avatarSize}
              className="cursor-pointer hover:opacity-80 "
            >
              {userName ? userName.charAt(0).toUpperCase() : <CameraOutlined />}
            </Avatar>
          )}
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
            {loading ? (
              <LoadingOutlined className="text-white text-2xl" />
            ) : (
              <CameraOutlined className="text-white text-2xl" />
            )}
          </div>
        </div>
      </Upload>

      <Modal
        title={t('common.cropAvatar')}
        open={cropModalVisible}
        onOk={handleCropConfirm}
        onCancel={() => setCropModalVisible(false)}
        centered
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        confirmLoading={loading}
      >
        {imageSrc && (
          <ReactCrop
            crop={crop}
            onChange={(_, percentCrop) => setCrop(percentCrop)}
            onComplete={setCompletedCrop}
            aspect={1}
            circularCrop
          >
            <img
              ref={imgRef}
              src={imageSrc}
              style={{ maxHeight: '400px', maxWidth: '100%' }}
              onLoad={() => {
                if (imgRef.current) {
                  const { width, height } = imgRef.current;
                  const size = Math.min(width, height) * 0.8;
                  const newCrop = {
                    unit: 'px' as const,
                    width: size,
                    height: size,
                    x: (width - size) / 2,
                    y: (height - size) / 2,
                  };
                  setCrop(newCrop);
                  // 设置默认的 completedCrop，确保首次加载时也能确认裁切
                  setCompletedCrop(newCrop);
                }
              }}
            />
          </ReactCrop>
        )}
      </Modal>
    </>
  );
};

export default ImageUploadWithCrop;
