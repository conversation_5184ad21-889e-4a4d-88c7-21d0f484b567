import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  DatePicker,
  Select,
  Upload,
  message,
  Row,
  Col,
  Space,
} from 'antd';
import { UploadOutlined, PlusOutlined } from '@ant-design/icons';
import { api, musicUtils } from '@/services';
import type { SubmitTrackRequest } from '@/types/api';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 提交音乐作品表单组件
 */
const SubmitTrackForm: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [mediaUrls, setMediaUrls] = useState<string[]>([]);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      // 验证数据
      const trackData: SubmitTrackRequest = {
        title: values.title,
        labelName: values.labelName,
        albumName: values.albumName,
        trackInfo: values.trackInfo,
        primaryLanguage: values.primaryLanguage,
        upc: values.upc,
        isrc: values.isrc,
        primaryGenreId: values.primaryGenreId,
        secondaryGenreId: values.secondaryGenreId,
        originalReleaseDate: values.originalReleaseDate.valueOf(),
        streetDate: values.streetDate.valueOf(),
        copyrightName: values.copyrightName,
        copyrightYear: values.copyrightYear,
        phonogramCopyright: values.phonogramCopyright,
        phonogramCopyrightYear: values.phonogramCopyrightYear,
        coverArtUrl: values.coverArtUrl,
        audioFormats: values.audioFormats || [],
        releaseOptions: values.releaseOptions || [],
        mediaUrls: mediaUrls,
      };

      // 验证数据
      const validation = musicUtils.validateTrackData(trackData);
      if (!validation.isValid) {
        message.error(`数据验证失败: ${validation.errors.join(', ')}`);
        return;
      }

      // 提交数据
      const response = await api.music.submitTrack(trackData);
      
      if (response.code === 200) {
        message.success(`音乐作品提交成功！作品ID: ${response.body.id}`);
        form.resetFields();
        setMediaUrls([]);
      } else {
        message.error('提交失败，请重试');
      }
    } catch (error) {
      message.error('提交失败，请检查网络连接');
      console.error('Error submitting track:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加媒体URL
  const addMediaUrl = () => {
    const url = form.getFieldValue('newMediaUrl');
    if (url && !mediaUrls.includes(url)) {
      setMediaUrls([...mediaUrls, url]);
      form.setFieldValue('newMediaUrl', '');
    }
  };

  // 移除媒体URL
  const removeMediaUrl = (index: number) => {
    setMediaUrls(mediaUrls.filter((_, i) => i !== index));
  };

  return (
    <div className="p-6">
      <Card title="提交音乐作品" className="max-w-4xl mx-auto">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            primaryLanguage: 'en',
            copyrightYear: new Date().getFullYear().toString(),
            phonogramCopyrightYear: new Date().getFullYear().toString(),
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="音乐标题"
                name="title"
                rules={[{ required: true, message: '请输入音乐标题' }]}
              >
                <Input placeholder="请输入音乐标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="唱片公司名称"
                name="labelName"
                rules={[{ required: true, message: '请输入唱片公司名称' }]}
              >
                <Input placeholder="请输入唱片公司名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="专辑名称"
                name="albumName"
                rules={[{ required: true, message: '请输入专辑名称' }]}
              >
                <Input placeholder="请输入专辑名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="主要语言"
                name="primaryLanguage"
                rules={[{ required: true, message: '请选择主要语言' }]}
              >
                <Select placeholder="请选择主要语言">
                  <Option value="en">英语</Option>
                  <Option value="zh">中文</Option>
                  <Option value="es">西班牙语</Option>
                  <Option value="fr">法语</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="音轨信息" name="trackInfo">
            <TextArea rows={3} placeholder="请输入音轨信息（可选）" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="UPC代码" name="upc">
                <Input placeholder="请输入UPC代码（可选）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="ISRC代码" name="isrc">
                <Input placeholder="请输入ISRC代码（可选）" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="主要音乐类型"
                name="primaryGenreId"
                rules={[{ required: true, message: '请选择主要音乐类型' }]}
              >
                <Select placeholder="请选择主要音乐类型">
                  <Option value="music.genre.pop">流行音乐</Option>
                  <Option value="music.genre.rock">摇滚音乐</Option>
                  <Option value="music.genre.electronic">电子音乐</Option>
                  <Option value="music.genre.classical">古典音乐</Option>
                  <Option value="music.genre.jazz">爵士音乐</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="次要音乐类型" name="secondaryGenreId">
                <Select placeholder="请选择次要音乐类型（可选）">
                  <Option value="music.genre.pop">流行音乐</Option>
                  <Option value="music.genre.rock">摇滚音乐</Option>
                  <Option value="music.genre.electronic">电子音乐</Option>
                  <Option value="music.genre.classical">古典音乐</Option>
                  <Option value="music.genre.jazz">爵士音乐</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="原始发布日期"
                name="originalReleaseDate"
                rules={[{ required: true, message: '请选择原始发布日期' }]}
              >
                <DatePicker className="w-full" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="街头发布日期"
                name="streetDate"
                rules={[{ required: true, message: '请选择街头发布日期' }]}
              >
                <DatePicker className="w-full" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="版权所有者名称"
                name="copyrightName"
                rules={[{ required: true, message: '请输入版权所有者名称' }]}
              >
                <Input placeholder="请输入版权所有者名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="版权年份"
                name="copyrightYear"
                rules={[{ required: true, message: '请输入版权年份' }]}
              >
                <Input placeholder="请输入版权年份" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="录音版权所有者"
                name="phonogramCopyright"
                rules={[{ required: true, message: '请输入录音版权所有者' }]}
              >
                <Input placeholder="请输入录音版权所有者" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="录音版权年份"
                name="phonogramCopyrightYear"
                rules={[{ required: true, message: '请输入录音版权年份' }]}
              >
                <Input placeholder="请输入录音版权年份" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="封面艺术URL"
            name="coverArtUrl"
            rules={[{ required: true, message: '请输入封面艺术URL' }]}
          >
            <Input placeholder="请输入封面艺术URL" />
          </Form.Item>

          <Form.Item label="音频格式" name="audioFormats">
            <Select mode="multiple" placeholder="请选择音频格式（可选）">
              <Option value="music.audio-format.supports-dolby-atmos">支持杜比全景声</Option>
              <Option value="music.audio-format.apple-digital-masters">Apple Digital Masters</Option>
            </Select>
          </Form.Item>

          <Form.Item label="发布选项" name="releaseOptions">
            <Select mode="multiple" placeholder="请选择发布选项（可选）">
              <Option value="music.release-option.enable-itunes-pre-order">启用iTunes预订</Option>
              <Option value="music.release-option.live-concert-recording">现场音乐会录音</Option>
              <Option value="music.release-option.remastered-recording">重新制作录音</Option>
            </Select>
          </Form.Item>

          <Form.Item label="媒体文件URL">
            <Space.Compact style={{ display: 'flex' }}>
              <Form.Item name="newMediaUrl" style={{ flex: 1, marginBottom: 0 }}>
                <Input placeholder="请输入媒体文件URL" />
              </Form.Item>
              <Button type="primary" icon={<PlusOutlined />} onClick={addMediaUrl}>
                添加
              </Button>
            </Space.Compact>
            {mediaUrls.length > 0 && (
              <div className="mt-2">
                {mediaUrls.map((url, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded mb-1">
                    <span className="truncate flex-1">{url}</span>
                    <Button 
                      type="link" 
                      danger 
                      size="small"
                      onClick={() => removeMediaUrl(index)}
                    >
                      删除
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              size="large"
              className="w-full"
            >
              提交音乐作品
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default SubmitTrackForm;
