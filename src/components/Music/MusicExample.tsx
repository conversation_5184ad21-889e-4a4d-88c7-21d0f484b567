import React, { useState, useEffect } from 'react';
import { Card, Button, Table, message, Pagination, Select } from 'antd';
import { api, musicUtils } from '@/services';
import type { Track, RankedTracksResponse, TracksByGenreResponse } from '@/types/api';

const { Option } = Select;

/**
 * 音乐API使用示例组件
 */
const MusicExample: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [tracksByGenre, setTracksByGenre] = useState<TracksByGenreResponse>({});
  const [rankedTracks, setRankedTracks] = useState<Track[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [rankType, setRankType] = useState<'revenue' | 'streams'>('revenue');

  // 获取按类型分组的音乐
  const fetchTracksByGenre = async () => {
    try {
      setLoading(true);
      const response = await api.music.getTracksByGenre(5);
      setTracksByGenre(response.body);
    } catch (error) {
      message.error('获取音乐列表失败');
      console.error('Error fetching tracks by genre:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取排名音乐列表
  const fetchRankedTracks = async (page: number = 1, size: number = 10) => {
    try {
      setLoading(true);
      let response: RankedTracksResponse;
      
      if (rankType === 'revenue') {
        const result = await api.music.getTracksByRevenue(page, size);
        response = result.body;
      } else {
        const result = await api.music.getTracksByStreams(page, size);
        response = result.body;
      }

      setRankedTracks(response.pageView.result);
      setPagination({
        current: response.pageView.pageNum,
        pageSize: response.pageView.pageSize,
        total: response.pageView.totalRows,
      });
    } catch (error) {
      message.error('获取排名音乐列表失败');
      console.error('Error fetching ranked tracks:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    fetchRankedTracks(page, pageSize || pagination.pageSize);
  };

  // 处理排名类型变化
  const handleRankTypeChange = (value: 'revenue' | 'streams') => {
    setRankType(value);
    fetchRankedTracks(1, pagination.pageSize);
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchTracksByGenre();
    fetchRankedTracks();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '封面',
      dataIndex: 'coverArtUrl',
      key: 'coverArtUrl',
      width: 80,
      render: (url: string) => (
        <img 
          src={url} 
          alt="封面" 
          style={{ width: 50, height: 50, objectFit: 'cover', borderRadius: 4 }}
          onError={(e) => {
            (e.target as HTMLImageElement).src = '/placeholder-music.png';
          }}
        />
      ),
    },
    {
      title: '歌曲名称',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '艺术家',
      dataIndex: 'artistStageName',
      key: 'artistStageName',
    },
    {
      title: '类型',
      dataIndex: 'genre',
      key: 'genre',
    },
    {
      title: rankType === 'revenue' ? '周期收入' : '周期播放量',
      dataIndex: rankType === 'revenue' ? 'periodicRevenue' : 'periodicStreams',
      key: rankType === 'revenue' ? 'periodicRevenue' : 'periodicStreams',
      render: (value: number) => 
        rankType === 'revenue' 
          ? musicUtils.formatRevenue(value)
          : musicUtils.formatStreams(value),
    },
    {
      title: rankType === 'revenue' ? '总收入' : '总播放量',
      dataIndex: rankType === 'revenue' ? 'totalRevenue' : 'totalStreams',
      key: rankType === 'revenue' ? 'totalRevenue' : 'totalStreams',
      render: (value: number) => 
        rankType === 'revenue' 
          ? musicUtils.formatRevenue(value)
          : musicUtils.formatStreams(value),
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      render: (timestamp: number | null) => 
        timestamp ? musicUtils.formatDate(timestamp) : '-',
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <Card title="音乐API使用示例" className="w-full">
        <div className="space-y-4">
          <div className="flex gap-4">
            <Button 
              type="primary" 
              onClick={fetchTracksByGenre}
              loading={loading}
            >
              获取按类型分组的音乐
            </Button>
            <Button 
              onClick={() => fetchRankedTracks()}
              loading={loading}
            >
              刷新排名列表
            </Button>
          </div>

          {/* 按类型分组的音乐展示 */}
          {Object.keys(tracksByGenre).length > 0 && (
            <Card title="按类型分组的音乐" size="small">
              {Object.entries(tracksByGenre).map(([genre, tracks]) => (
                <div key={genre} className="mb-4">
                  <h4 className="font-medium mb-2">{genre}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {tracks.map((track) => (
                      <Card key={track.id} size="small" className="hover:shadow-md transition-shadow">
                        <div className="flex items-center space-x-3">
                          <img 
                            src={track.coverArtUrl} 
                            alt={track.title}
                            className="w-12 h-12 object-cover rounded"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder-music.png';
                            }}
                          />
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{track.title}</p>
                            <p className="text-sm text-gray-500 truncate">{track.artistStageName}</p>
                            <p className="text-xs text-gray-400">
                              {musicUtils.formatRevenue(track.totalRevenue)} | {musicUtils.formatStreams(track.totalStreams)}
                            </p>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </Card>
          )}

          {/* 排名音乐列表 */}
          <Card 
            title={
              <div className="flex items-center justify-between">
                <span>音乐排名列表</span>
                <Select
                  value={rankType}
                  onChange={handleRankTypeChange}
                  style={{ width: 120 }}
                >
                  <Option value="revenue">按收入排名</Option>
                  <Option value="streams">按播放量排名</Option>
                </Select>
              </div>
            }
            size="small"
          >
            <Table
              columns={columns}
              dataSource={rankedTracks}
              rowKey="id"
              loading={loading}
              pagination={false}
              scroll={{ x: 800 }}
            />
            <div className="mt-4 flex justify-center">
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePaginationChange}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
              />
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default MusicExample;
