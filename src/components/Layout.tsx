import React, { useEffect, useMemo, useState } from 'react';
import { Layout as AntLayout } from 'antd';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import Header from '@/components/Header';
import { MAIN_MENU_ITEMS } from '@/config/menuConfig';
import Profile from '@/components/Profile';
import { useLanguage } from '@/hooks/useLanguage';
import i18n from '@/locales';

import {
  SoundOutlined,
  FolderOutlined,
  WalletOutlined,
  ShoppingOutlined,
  UploadOutlined,
} from '@ant-design/icons';

const { Content, Sider, Header: AntHeader } = AntLayout;

const getNavigationItems = (t: any) => [
  {
    key: 'music-market',
    label: i18n.t('common.navigation.musicMarket'),
    url: '/music-market',
    icon: SoundOutlined,
  },
  {
    key: 'my-assets',
    label: i18n.t('common.navigation.myAssets'),
    url: '/my-assets',
    icon: FolderOutlined,
  },
  {
    key: 'my-balance',
    label: i18n.t('common.navigation.myBalance'),
    url: '/my-balance',
    icon: WalletOutlined,
  },
  {
    key: 'my-orders',
    label: i18n.t('common.navigation.myOrders'),
    url: '/my-orders',
    icon: ShoppingOutlined,
  },
  {
    key: 'submit-music',
    label: i18n.t('common.navigation.submitMusic'),
    url: '/submit-music',
    icon: UploadOutlined,
  },
];

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useLanguage();
  const { isAuthenticated } = useAuthStore();

  // 初始化认证状态
  useEffect(() => {
    useAuthStore.getState().initializeAuth();
  }, []);

  const navigationItems = getNavigationItems(t);

  const [profileVisible, setProfileVisible] = useState(false);
  const handleOpenUserProfile = () => {
    setProfileVisible(true);
  };

  const handleNavigate = (url: string) => {
    navigate(url);
  };

  return (
    <div className="bg-black min-h-screen">
      <AntLayout className="min-h-screen bg-black">
        {isAuthenticated && (
          <AntHeader className="bg-black border-b-0 p-0 h-auto">
            <Header fixed={false} onClick={handleOpenUserProfile} />
          </AntHeader>
        )}
        <AntLayout className="bg-black">
          {isAuthenticated && (
            <Sider width={307} className="bg-[#0d0d0d] pt-8 px-4">
              <div className="space-y-1">
                {navigationItems.map(item => {
                  const isActive = location.pathname === item.url;
                  return (
                    <div
                      key={item.key}
                      className={`
                        relative px-6 py-3 rounded-md cursor-pointer transition-colors
                        ${
                          isActive
                            ? 'text-primary border-2 border-primary border-solid bg-[#1a1a1a]'
                            : 'text-[#999999] hover:text-white hover:bg-[#1a1a1a]'
                        }
                      `}
                      onClick={() => handleNavigate(item.url)}
                    >
                      <div className="flex items-center">
                        <item.icon className="w-6 h-6 mr-3" />
                        <span className="text-18px font-bold">
                          {item.label}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Sider>
          )}
          <Content className="flex-1 bg-[#0d0d0d]">
            <Outlet />
          </Content>
        </AntLayout>
      </AntLayout>
      <Profile
        visible={profileVisible}
        onClose={() => setProfileVisible(false)}
      />
    </div>
  );
};

export default Layout;
