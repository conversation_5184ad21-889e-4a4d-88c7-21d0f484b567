import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { But<PERSON>, Divider, Layout as AntLayout } from 'antd';
import Header from '@/components/Header';
import homeBg from '@/assets/images/homebg.png';
import cover2Icon from '@/assets/images/cover2.png';
import rectangle1 from '@/assets/images/rectangle-1.png';
import rectangle2 from '@/assets/images/rectangle-2.png';
import rectangle3 from '@/assets/images/rectangle-3.png';

const { Content, Sider, Header: AntHeader } = AntLayout;
// 音乐分类数据
const musicCategories = [
  {
    id: 'pop',
    name: 'Pop',
    cover: rectangle1,
    tracks: [
      {
        id: 1,
        title: 'Midnight City Lights',
        artist: 'Beats by Jun<PERSON>',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 2,
        title: 'Cultural Music Exchange',
        artist: '<PERSON>',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 3,
        title: 'Electronic',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 4,
        title: 'Music In Everyday Life',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 5,
        title: 'MONTERO (Call Me by Your Name)',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'rap',
    name: 'Rap',
    cover: rectangle2,
    tracks: [
      {
        id: 6,
        title: 'Midnight City',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 7,
        title: 'Super Freaky Girl',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 8,
        title: 'Super Freaky Girl',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 9,
        title: "I Ain't Worried",
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 10,
        title: 'She Loves You',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 11,
        title: 'As It Was',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'bass-music',
    name: 'Bass Music',
    cover: rectangle3,
    tracks: [
      {
        id: 12,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 13,
        title: 'Super Freaky Girl',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 14,
        title: "I Ain't Worried",
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 15,
        title: 'She Loves You',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 16,
        title: 'As It Was',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'jazz',
    name: 'Jazz',
    cover: rectangle1,
    tracks: [
      {
        id: 17,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 18,
        title: 'Quevedo: Bzrp Music Sessions, Vol. 52',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 19,
        title: "I'm Good (Blue)",
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 20,
        title: "I'm Good (Blue)",
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 21,
        title: 'As It Was',
        artist: 'Taylor Swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'ambient',
    name: 'Ambient',
    cover: rectangle1,
    tracks: [
      {
        id: 22,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 23,
        title: 'I Like You',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 24,
        title: "I Ain't Worried",
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 25,
        title: 'Quevedo: Bzrp Music Sessions, Vol. 52',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 26,
        title: 'As It Was',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'noise',
    name: 'Noise',
    cover: rectangle1,
    tracks: [
      {
        id: 27,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 28,
        title: "I Ain't Worried",
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 29,
        title: 'Super Freaky Girl',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 30,
        title: 'Shut Down',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 31,
        title: 'She Loves You',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'jungle',
    name: 'Jungle',
    cover: rectangle1,
    tracks: [
      {
        id: 32,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 33,
        title: "I Ain't Worried",
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 34,
        title: "I'm Good (Blue)",
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 35,
        title: 'Shut Down',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 36,
        title: 'About Damn',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
  {
    id: 'ambient2',
    name: 'Ambient',
    cover: rectangle3,
    tracks: [
      {
        id: 37,
        title: 'Midnight City Lights',
        artist: 'Beats by Juniper',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 38,
        title: 'I Like You',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 39,
        title: "I Ain't Worried",
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 40,
        title: 'Quevedo: Bzrp Music Sessions, Vol. 52',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 41,
        title: 'About Damn',
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
      {
        id: 42,
        title: "I'm Good (Blue)",
        artist: 'taylor swift',
        cover: '/api/placeholder/150/150',
      },
    ],
  },
];

// 图表数据
const weeklyRevenueData = [
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '19024.30',
  },
  { title: "I'm Good (Blue)", genre: 'Jazz', revenue: '15978.80' },
  {
    title: '月夜舞·光影中的我们音乐体验',
    genre: 'Bass Music',
    revenue: '10103.00',
  },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Ambient',
    revenue: '8124.31',
  },
  { title: 'Cultural Music Exchange', genre: 'Jungle', revenue: '7009.00' },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Noise',
    revenue: '5000.11',
  },
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '3002.21',
  },
];

const weeklyStreamingData = [
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '19024.30',
  },
  { title: "I'm Good (Blue)", genre: 'Jazz', revenue: '15978.80' },
  {
    title: '月夜舞·光影中的我们音乐体验',
    genre: 'Bass Music',
    revenue: '10103.00',
  },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Ambient',
    revenue: '8124.31',
  },
  { title: 'Cultural Music Exchange', genre: 'Jungle', revenue: '7009.00' },
  {
    title: 'MONTERO (Call Me by Your Name)',
    genre: 'Noise',
    revenue: '5000.11',
  },
  {
    title: 'Quevedo: Bzrp Music Sessions, Vo...',
    genre: 'Pop',
    revenue: '3002.21',
  },
];

const Home: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div
      className="min-h-screen bg-cover bg-#d3d3d3 bg-center bg-no-repeat "
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),url(${homeBg})`,
      }}
    >
      <div className="max-w-[1920px] mx-auto">
        <AntHeader className="border-b-0 p-0 h-auto !bg-transparent">
          <Header fixed={false} bgColor="bg-transparent" />
        </AntHeader>
        <div className="flex  ">
          {/* 主内容区域 */}
          <div className="flex-1 p-8">
            {/* 音乐分类网格 */}
            <div className="grid grid-cols-3 min-[1600px]:grid-cols-4  gap-8 gap-y-10">
              {musicCategories.map(category => (
                <div
                  key={category.id}
                  className="space-y-4 h-421px bg-[#0D0D0D] backdrop-blur-md rounded-20px p-5 "
                >
                  {/* 分类标题 */}
                  <div className="flex  justify-between">
                    <h2 className="text-primary text-3xl  font-bold">
                      {category.name}
                    </h2>
                    <span>See all</span>
                  </div>
                  <div className="flex gap-4 items-center">
                    <img
                      src={category.cover}
                      alt="cover"
                      className="w-105px h-105px rounded-10px"
                    />
                    <div className="flex flex-col gap-16px ">
                      <div className="text-white text-16px  font-bold">
                        {category.tracks[0].title}
                      </div>
                      <div className="text-label">
                        {category.tracks[0].artist}
                      </div>
                    </div>
                  </div>

                  {/* 音乐列表 */}
                  <div className="space-y-3">
                    {category.tracks.slice(0, 3).map(track => (
                      <div
                        key={track.id}
                        className="flex items-center space-x-3 group cursor-pointer"
                      >
                        {/* 音乐信息 */}
                        <div className="flex-1 min-w-0">
                          <h3 className="text-white text-14px font-medium truncate group-hover:text-[#ff6b35] transition-colors">
                            {track.title}
                          </h3>
                          {track.artist && (
                            <p className="text-[#999] text-12px truncate mt-1">
                              {track.artist}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 右侧边栏 */}
          {/* <div className="min-w-472px  p-8 flex flex-col gap-10"> */}
          <div className="min-w-472px  p-8 ">
            <div className="grid grid-cols-1 grid-rows-2  gap-8 gap-y-10">
              {/* Weekly Revenue Chart */}
              <div className=" bg-[#0d0d0d] p-5 rounded-20px h-421px">
                <div className="flex  justify-between ">
                  <h3 className="text-primary  font-bold">
                    Weekly Revenue Chart
                  </h3>
                  <span>See all</span>
                </div>
                <Divider className="bg-[#262626]" />
                <div className="space-y-3">
                  {weeklyRevenueData.slice(0, 5).map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between py-2"
                    >
                      <div className="flex-1 min-w-0 pr-4">
                        <div className="text-white text-12px font-medium truncate">
                          {item.title}
                        </div>
                        <div className="text-[#999] text-10px mt-1">
                          {item.genre}
                        </div>
                      </div>
                      <div className="text-white text-12px font-semibold">
                        {item.revenue}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Weekly Streaming Chart */}
              <div className=" bg-[#0d0d0d] p-5 rounded-20px h-421px">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex  justify-between ">
                    <h3 className="text-primary  font-bold">
                      Weekly Streaming Chart
                    </h3>
                    <span>See all</span>
                  </div>
                </div>
                <Divider className="bg-[#262626]" />
                <div className="space-y-3">
                  {weeklyStreamingData.slice(0, 5).map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between py-2"
                    >
                      <div className="flex-1 min-w-0 pr-4">
                        <div className="text-white text-12px font-medium truncate">
                          {item.title}
                        </div>
                        <div className="text-[#999] text-10px mt-1">
                          {item.genre}
                        </div>
                      </div>
                      <div className="text-white text-12px font-semibold">
                        {item.revenue}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
